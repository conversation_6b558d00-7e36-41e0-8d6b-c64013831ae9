import java.util.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.io.*;
import java.math.BigDecimal;
import java.util.stream.Collectors;

/**
 * 图书管理系统
 * 包含图书、作者、借阅者和图书馆管理功能
 */
public class LibraryManagementSystem {
    public static void main(String[] args) {
        Library library = new Library("中央图书馆");
        Scanner scanner = new Scanner(System.in);
        
        // 初始化示例数据
        library.initializeSampleData();
        
        while (true) {
            System.out.println("\n=== 图书馆管理系统 ===");
            System.out.println("1. 图书管理");
            System.out.println("2. 读者管理");
            System.out.println("3. 借阅管理");
            System.out.println("4. 查询与统计");
            System.out.println("5. 系统管理");
            System.out.println("0. 退出");
            System.out.print("请选择操作: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine(); // 消耗换行符
            
            switch (choice) {
                case 1:
                    bookManagementMenu(library, scanner);
                    break;
                case 2:
                    readerManagementMenu(library, scanner);
                    break;
                case 3:
                    borrowingManagementMenu(library, scanner);
                    break;
                case 4:
                    queryAndStatisticsMenu(library, scanner);
                    break;
                case 5:
                    systemManagementMenu(library, scanner);
                    break;
                case 0:
                    System.out.println("谢谢使用图书馆管理系统！");
                    library.saveDataToFile();
                    return;
                default:
                    System.out.println("无效选项，请重新选择。");
            }
        }
    }
    
    private static void bookManagementMenu(Library library, Scanner scanner) {
        while (true) {
            System.out.println("\n=== 图书管理 ===");
            System.out.println("1. 添加图书");
            System.out.println("2. 删除图书");
            System.out.println("3. 修改图书信息");
            System.out.println("4. 查找图书");
            System.out.println("5. 显示所有图书");
            System.out.println("6. 按分类显示图书");
            System.out.println("7. 图书库存管理");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择操作: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine();
            
            switch (choice) {
                case 1:
                    addBook(library, scanner);
                    break;
                case 2:
                    deleteBook(library, scanner);
                    break;
                case 3:
                    updateBook(library, scanner);
                    break;
                case 4:
                    searchBooks(library, scanner);
                    break;
                case 5:
                    library.displayAllBooks();
                    break;
                case 6:
                    displayBooksByCategory(library, scanner);
                    break;
                case 7:
                    bookInventoryManagement(library, scanner);
                    break;
                case 0:
                    return;
                default:
                    System.out.println("无效选项，请重新选择。");
            }
        }
    }
    
    private static void readerManagementMenu(Library library, Scanner scanner) {
        while (true) {
            System.out.println("\n=== 读者管理 ===");
            System.out.println("1. 添加读者");
            System.out.println("2. 删除读者");
            System.out.println("3. 修改读者信息");
            System.out.println("4. 查找读者");
            System.out.println("5. 显示所有读者");
            System.out.println("6. 显示读者借阅历史");
            System.out.println("7. 读者积分管理");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择操作: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine();
            
            switch (choice) {
                case 1:
                    addReader(library, scanner);
                    break;
                case 2:
                    deleteReader(library, scanner);
                    break;
                case 3:
                    updateReader(library, scanner);
                    break;
                case 4:
                    searchReader(library, scanner);
                    break;
                case 5:
                    library.displayAllReaders();
                    break;
                case 6:
                    displayReaderHistory(library, scanner);
                    break;
                case 7:
                    readerPointsManagement(library, scanner);
                    break;
                case 0:
                    return;
                default:
                    System.out.println("无效选项，请重新选择。");
            }
        }
    }
    
    private static void borrowingManagementMenu(Library library, Scanner scanner) {
        while (true) {
            System.out.println("\n=== 借阅管理 ===");
            System.out.println("1. 借阅图书");
            System.out.println("2. 归还图书");
            System.out.println("3. 续借图书");
            System.out.println("4. 显示借阅记录");
            System.out.println("5. 显示逾期记录");
            System.out.println("6. 计算罚金");
            System.out.println("7. 预约图书");
            System.out.println("8. 取消预约");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择操作: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine();
            
            switch (choice) {
                case 1:
                    borrowBook(library, scanner);
                    break;
                case 2:
                    returnBook(library, scanner);
                    break;
                case 3:
                    renewBook(library, scanner);
                    break;
                case 4:
                    library.displayBorrowingRecords();
                    break;
                case 5:
                    library.displayOverdueRecords();
                    break;
                case 6:
                    calculateFines(library, scanner);
                    break;
                case 7:
                    reserveBook(library, scanner);
                    break;
                case 8:
                    cancelReservation(library, scanner);
                    break;
                case 0:
                    return;
                default:
                    System.out.println("无效选项，请重新选择。");
            }
        }
    }
    
    private static void queryAndStatisticsMenu(Library library, Scanner scanner) {
        while (true) {
            System.out.println("\n=== 查询与统计 ===");
            System.out.println("1. 生成基础报告");
            System.out.println("2. 借阅统计分析");
            System.out.println("3. 热门图书排行");
            System.out.println("4. 活跃读者排行");
            System.out.println("5. 收入统计");
            System.out.println("6. 高级搜索");
            System.out.println("7. 导出数据");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择操作: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine();
            
            switch (choice) {
                case 1:
                    library.generateReport();
                    break;
                case 2:
                    library.generateBorrowingStatistics();
                    break;
                case 3:
                    library.displayPopularBooks();
                    break;
                case 4:
                    library.displayActiveReaders();
                    break;
                case 5:
                    library.generateIncomeReport();
                    break;
                case 6:
                    advancedSearch(library, scanner);
                    break;
                case 7:
                    exportData(library, scanner);
                    break;
                case 0:
                    return;
                default:
                    System.out.println("无效选项，请重新选择。");
            }
        }
    }
    
    private static void systemManagementMenu(Library library, Scanner scanner) {
        while (true) {
            System.out.println("\n=== 系统管理 ===");
            System.out.println("1. 备份数据");
            System.out.println("2. 恢复数据");
            System.out.println("3. 清理过期数据");
            System.out.println("4. 系统设置");
            System.out.println("5. 用户权限管理");
            System.out.println("6. 日志查看");
            System.out.println("0. 返回主菜单");
            System.out.print("请选择操作: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine();
            
            switch (choice) {
                case 1:
                    library.backupData();
                    break;
                case 2:
                    library.restoreData();
                    break;
                case 3:
                    library.cleanExpiredData();
                    break;
                case 4:
                    systemSettings(library, scanner);
                    break;
                case 5:
                    userPermissionManagement(library, scanner);
                    break;
                case 6:
                    library.viewLogs();
                    break;
                case 0:
                    return;
                default:
                    System.out.println("无效选项，请重新选择。");
            }
        }
    }
    
    private static void addBook(Library library, Scanner scanner) {
        System.out.print("书名: ");
        String title = scanner.nextLine();
        System.out.print("作者: ");
        String authorName = scanner.nextLine();
        System.out.print("ISBN: ");
        String isbn = scanner.nextLine();
        System.out.print("分类: ");
        String category = scanner.nextLine();
        System.out.print("出版年份: ");
        int year = scanner.nextInt();
        scanner.nextLine();
        
        Author author = new Author(authorName);
        Book book = new Book(title, author, isbn, category, year);
        library.addBook(book);
        System.out.println("图书添加成功！");
    }
    
    private static void searchBooks(Library library, Scanner scanner) {
        System.out.print("输入搜索关键词（书名或作者）: ");
        String keyword = scanner.nextLine();
        List<Book> results = library.searchBooks(keyword);
        
        if (results.isEmpty()) {
            System.out.println("未找到相关图书。");
        } else {
            System.out.println("搜索结果:");
            for (Book book : results) {
                System.out.println(book);
            }
        }
    }
    
    private static void borrowBook(Library library, Scanner scanner) {
        System.out.print("读者ID: ");
        String readerId = scanner.nextLine();
        System.out.print("图书ISBN: ");
        String isbn = scanner.nextLine();
        
        if (library.borrowBook(readerId, isbn)) {
            System.out.println("借阅成功！");
        } else {
            System.out.println("借阅失败！请检查读者ID和图书ISBN。");
        }
    }
    
    private static void returnBook(Library library, Scanner scanner) {
        System.out.print("读者ID: ");
        String readerId = scanner.nextLine();
        System.out.print("图书ISBN: ");
        String isbn = scanner.nextLine();
        
        if (library.returnBook(readerId, isbn)) {
            System.out.println("归还成功！");
        } else {
            System.out.println("归还失败！请检查借阅记录。");
        }
    }
    
    private static void addReader(Library library, Scanner scanner) {
        System.out.print("读者姓名: ");
        String name = scanner.nextLine();
        System.out.print("读者ID: ");
        String id = scanner.nextLine();
        System.out.print("联系电话: ");
        String phone = scanner.nextLine();
        System.out.print("邮箱: ");
        String email = scanner.nextLine();
        
        Reader reader = new Reader(name, id, phone, email);
        library.addReader(reader);
        System.out.println("读者添加成功！");
    }
    
    // 新增方法实现
    private static void deleteBook(Library library, Scanner scanner) {
        System.out.print("请输入要删除的图书ISBN: ");
        String isbn = scanner.nextLine();
        if (library.deleteBook(isbn)) {
            System.out.println("图书删除成功！");
        } else {
            System.out.println("删除失败！图书不存在或正在被借阅。");
        }
    }
    
    private static void updateBook(Library library, Scanner scanner) {
        System.out.print("请输入要修改的图书ISBN: ");
        String isbn = scanner.nextLine();
        library.updateBook(isbn, scanner);
    }
    
    private static void displayBooksByCategory(Library library, Scanner scanner) {
        System.out.print("请输入分类名称: ");
        String category = scanner.nextLine();
        library.displayBooksByCategory(category);
    }
    
    private static void bookInventoryManagement(Library library, Scanner scanner) {
        System.out.println("\n=== 图书库存管理 ===");
        System.out.println("1. 增加图书副本");
        System.out.println("2. 减少图书副本");
        System.out.println("3. 查看库存状态");
        System.out.print("请选择操作: ");
        
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        switch (choice) {
            case 1:
                System.out.print("图书ISBN: ");
                String isbn = scanner.nextLine();
                System.out.print("增加数量: ");
                int addCount = scanner.nextInt();
                library.addBookCopies(isbn, addCount);
                break;
            case 2:
                System.out.print("图书ISBN: ");
                isbn = scanner.nextLine();
                System.out.print("减少数量: ");
                int removeCount = scanner.nextInt();
                library.removeBookCopies(isbn, removeCount);
                break;
            case 3:
                library.displayInventoryStatus();
                break;
        }
    }
    
    private static void deleteReader(Library library, Scanner scanner) {
        System.out.print("请输入要删除的读者ID: ");
        String readerId = scanner.nextLine();
        if (library.deleteReader(readerId)) {
            System.out.println("读者删除成功！");
        } else {
            System.out.println("删除失败！读者不存在或仍有未归还图书。");
        }
    }
    
    private static void updateReader(Library library, Scanner scanner) {
        System.out.print("请输入要修改的读者ID: ");
        String readerId = scanner.nextLine();
        library.updateReader(readerId, scanner);
    }
    
    private static void searchReader(Library library, Scanner scanner) {
        System.out.print("输入搜索关键词（姓名或ID）: ");
        String keyword = scanner.nextLine();
        library.searchReader(keyword);
    }
    
    private static void displayReaderHistory(Library library, Scanner scanner) {
        System.out.print("请输入读者ID: ");
        String readerId = scanner.nextLine();
        library.displayReaderBorrowingHistory(readerId);
    }
    
    private static void readerPointsManagement(Library library, Scanner scanner) {
        System.out.print("请输入读者ID: ");
        String readerId = scanner.nextLine();
        library.displayReaderPoints(readerId);
    }
    
    private static void renewBook(Library library, Scanner scanner) {
        System.out.print("读者ID: ");
        String readerId = scanner.nextLine();
        System.out.print("图书ISBN: ");
        String isbn = scanner.nextLine();
        
        if (library.renewBook(readerId, isbn)) {
            System.out.println("续借成功！");
        } else {
            System.out.println("续借失败！请检查借阅记录或续借条件。");
        }
    }
    
    private static void calculateFines(Library library, Scanner scanner) {
        System.out.print("请输入读者ID: ");
        String readerId = scanner.nextLine();
        library.calculateAndDisplayFines(readerId);
    }
    
    private static void reserveBook(Library library, Scanner scanner) {
        System.out.print("读者ID: ");
        String readerId = scanner.nextLine();
        System.out.print("图书ISBN: ");
        String isbn = scanner.nextLine();
        
        if (library.reserveBook(readerId, isbn)) {
            System.out.println("预约成功！");
        } else {
            System.out.println("预约失败！请检查图书状态。");
        }
    }
    
    private static void cancelReservation(Library library, Scanner scanner) {
        System.out.print("读者ID: ");
        String readerId = scanner.nextLine();
        System.out.print("图书ISBN: ");
        String isbn = scanner.nextLine();
        
        if (library.cancelReservation(readerId, isbn)) {
            System.out.println("取消预约成功！");
        } else {
            System.out.println("取消预约失败！未找到相关预约记录。");
        }
    }
    
    private static void advancedSearch(Library library, Scanner scanner) {
        System.out.println("\n=== 高级搜索 ===");
        System.out.println("1. 按价格区间搜索");
        System.out.println("2. 按出版年份搜索");
        System.out.println("3. 按多条件组合搜索");
        System.out.print("请选择搜索类型: ");
        
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        switch (choice) {
            case 1:
                System.out.print("最低价格: ");
                double minPrice = scanner.nextDouble();
                System.out.print("最高价格: ");
                double maxPrice = scanner.nextDouble();
                library.searchBooksByPriceRange(minPrice, maxPrice);
                break;
            case 2:
                System.out.print("起始年份: ");
                int startYear = scanner.nextInt();
                System.out.print("结束年份: ");
                int endYear = scanner.nextInt();
                library.searchBooksByYearRange(startYear, endYear);
                break;
            case 3:
                library.multiCriteriaSearch(scanner);
                break;
        }
    }
    
    private static void exportData(Library library, Scanner scanner) {
        System.out.println("\n=== 数据导出 ===");
        System.out.println("1. 导出图书清单");
        System.out.println("2. 导出读者信息");
        System.out.println("3. 导出借阅记录");
        System.out.println("4. 导出统计报告");
        System.out.print("请选择导出类型: ");
        
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        switch (choice) {
            case 1:
                library.exportBooksToCSV();
                break;
            case 2:
                library.exportReadersToCSV();
                break;
            case 3:
                library.exportBorrowingRecordsToCSV();
                break;
            case 4:
                library.exportStatisticsReport();
                break;
        }
    }
    
    private static void systemSettings(Library library, Scanner scanner) {
        System.out.println("\n=== 系统设置 ===");
        System.out.println("1. 修改借阅期限");
        System.out.println("2. 修改最大借阅数量");
        System.out.println("3. 修改罚金标准");
        System.out.println("4. 查看当前设置");
        System.out.print("请选择操作: ");
        
        int choice = scanner.nextInt();
        scanner.nextLine();
        
        library.systemSettings(choice, scanner);
    }
    
    private static void userPermissionManagement(Library library, Scanner scanner) {
        System.out.println("\n=== 用户权限管理 ===");
        System.out.println("功能开发中...");
    }
}

/**
 * 图书类
 */
class Book {
    private String title;
    private Author author;
    private String isbn;
    private String category;
    private int publicationYear;
    private boolean isAvailable;
    private LocalDate addedDate;
    private String publisher;
    private double price;
    private int totalCopies;
    private int availableCopies;
    private String description;
    private List<String> keywords;
    private int borrowCount;
    private double rating;
    private List<String> reviews;
    private String location; // 书架位置
    
    public Book(String title, Author author, String isbn, String category, int publicationYear) {
        this.title = title;
        this.author = author;
        this.isbn = isbn;
        this.category = category;
        this.publicationYear = publicationYear;
        this.isAvailable = true;
        this.addedDate = LocalDate.now();
        this.totalCopies = 1;
        this.availableCopies = 1;
        this.borrowCount = 0;
        this.rating = 0.0;
        this.keywords = new ArrayList<>();
        this.reviews = new ArrayList<>();
        this.price = 0.0;
        this.publisher = "";
        this.description = "";
        this.location = "";
    }
    
    public Book(String title, Author author, String isbn, String category, int publicationYear, 
                String publisher, double price, int totalCopies, String description) {
        this(title, author, isbn, category, publicationYear);
        this.publisher = publisher;
        this.price = price;
        this.totalCopies = totalCopies;
        this.availableCopies = totalCopies;
        this.description = description;
    }
    
    // Getters and Setters
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public Author getAuthor() { return author; }
    public void setAuthor(Author author) { this.author = author; }
    
    public String getIsbn() { return isbn; }
    public void setIsbn(String isbn) { this.isbn = isbn; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public int getPublicationYear() { return publicationYear; }
    public void setPublicationYear(int publicationYear) { this.publicationYear = publicationYear; }
    
    public boolean isAvailable() { return availableCopies > 0; }
    public void setAvailable(boolean available) { this.isAvailable = available; }
    
    public LocalDate getAddedDate() { return addedDate; }
    
    public String getPublisher() { return publisher; }
    public void setPublisher(String publisher) { this.publisher = publisher; }
    
    public double getPrice() { return price; }
    public void setPrice(double price) { this.price = price; }
    
    public int getTotalCopies() { return totalCopies; }
    public void setTotalCopies(int totalCopies) { this.totalCopies = totalCopies; }
    
    public int getAvailableCopies() { return availableCopies; }
    public void setAvailableCopies(int availableCopies) { this.availableCopies = availableCopies; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public List<String> getKeywords() { return new ArrayList<>(keywords); }
    public void addKeyword(String keyword) { keywords.add(keyword); }
    
    public int getBorrowCount() { return borrowCount; }
    public void incrementBorrowCount() { this.borrowCount++; }
    
    public double getRating() { return rating; }
    public void setRating(double rating) { this.rating = rating; }
    
    public List<String> getReviews() { return new ArrayList<>(reviews); }
    public void addReview(String review) { reviews.add(review); }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public boolean borrowCopy() {
        if (availableCopies > 0) {
            availableCopies--;
            incrementBorrowCount();
            return true;
        }
        return false;
    }
    
    public void returnCopy() {
        if (availableCopies < totalCopies) {
            availableCopies++;
        }
    }
    
    @Override
    public String toString() {
        return String.format("《%s》- 作者: %s, ISBN: %s, 分类: %s, 出版年份: %d, 出版社: %s, 价格: ¥%.2f, 可借数量: %d/%d, 借阅次数: %d, 评分: %.1f, 位置: %s",
                title, author.getName(), isbn, category, publicationYear, publisher, price,
                availableCopies, totalCopies, borrowCount, rating, location);
    }
    
    public String getDetailedInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 图书详细信息 ===\n");
        sb.append("书名: ").append(title).append("\n");
        sb.append("作者: ").append(author.getName()).append("\n");
        sb.append("ISBN: ").append(isbn).append("\n");
        sb.append("分类: ").append(category).append("\n");
        sb.append("出版年份: ").append(publicationYear).append("\n");
        sb.append("出版社: ").append(publisher).append("\n");
        sb.append("价格: ¥").append(String.format("%.2f", price)).append("\n");
        sb.append("总册数: ").append(totalCopies).append("\n");
        sb.append("可借册数: ").append(availableCopies).append("\n");
        sb.append("借阅次数: ").append(borrowCount).append("\n");
        sb.append("评分: ").append(String.format("%.1f", rating)).append("\n");
        sb.append("书架位置: ").append(location).append("\n");
        sb.append("描述: ").append(description).append("\n");
        if (!keywords.isEmpty()) {
            sb.append("关键词: ").append(String.join(", ", keywords)).append("\n");
        }
        return sb.toString();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Book book = (Book) obj;
        return Objects.equals(isbn, book.isbn);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(isbn);
    }
}

/**
 * 作者类
 */
class Author {
    private String name;
    private String biography;
    private List<Book> books;
    
    public Author(String name) {
        this.name = name;
        this.books = new ArrayList<>();
        this.biography = "";
    }
    
    public Author(String name, String biography) {
        this.name = name;
        this.biography = biography;
        this.books = new ArrayList<>();
    }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getBiography() { return biography; }
    public void setBiography(String biography) { this.biography = biography; }
    
    public List<Book> getBooks() { return new ArrayList<>(books); }
    public void addBook(Book book) { books.add(book); }
    
    @Override
    public String toString() {
        return name + " (著作数量: " + books.size() + ")";
    }
}

/**
 * 读者类
 */
class Reader {
    private String name;
    private String id;
    private String phone;
    private String email;
    private LocalDate registrationDate;
    private List<BorrowingRecord> borrowingHistory;
    private static final int MAX_BORROWED_BOOKS = 5;
    private int points; // 积分
    private String membershipLevel; // 会员等级
    private double fineAmount; // 欠款金额
    private boolean isActive; // 账户状态
    private String address;
    private LocalDate lastBorrowDate;
    private int totalBorrowedBooks;
    private List<String> reservedBooks; // 预约图书列表
    private Map<String, LocalDate> reservationDates; // 预约日期
    
    public Reader(String name, String id, String phone, String email) {
        this.name = name;
        this.id = id;
        this.phone = phone;
        this.email = email;
        this.registrationDate = LocalDate.now();
        this.borrowingHistory = new ArrayList<>();
        this.points = 100; // 新用户初始积分
        this.membershipLevel = "普通会员";
        this.fineAmount = 0.0;
        this.isActive = true;
        this.address = "";
        this.totalBorrowedBooks = 0;
        this.reservedBooks = new ArrayList<>();
        this.reservationDates = new HashMap<>();
    }
    
    public Reader(String name, String id, String phone, String email, String address) {
        this(name, id, phone, email);
        this.address = address;
    }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public LocalDate getRegistrationDate() { return registrationDate; }
    
    public List<BorrowingRecord> getBorrowingHistory() { return new ArrayList<>(borrowingHistory); }
    public void addBorrowingRecord(BorrowingRecord record) { 
        borrowingHistory.add(record);
        this.lastBorrowDate = LocalDate.now();
        this.totalBorrowedBooks++;
        addPoints(10); // 每次借阅获得10积分
    }
    
    public int getCurrentBorrowedCount() {
        return (int) borrowingHistory.stream()
                .filter(record -> record.getReturnDate() == null)
                .count();
    }
    
    public boolean canBorrowMore() {
        return getCurrentBorrowedCount() < getMaxBorrowedBooks() && isActive && fineAmount == 0;
    }
    
    public int getMaxBorrowedBooks() {
        switch (membershipLevel) {
            case "VIP会员": return 10;
            case "高级会员": return 8;
            case "普通会员": return 5;
            default: return 3;
        }
    }
    
    public int getPoints() { return points; }
    public void addPoints(int points) { 
        this.points += points;
        updateMembershipLevel();
    }
    public void deductPoints(int points) { 
        this.points = Math.max(0, this.points - points);
        updateMembershipLevel();
    }
    
    public String getMembershipLevel() { return membershipLevel; }
    private void updateMembershipLevel() {
        if (points >= 1000) {
            membershipLevel = "VIP会员";
        } else if (points >= 500) {
            membershipLevel = "高级会员";
        } else if (points >= 100) {
            membershipLevel = "普通会员";
        } else {
            membershipLevel = "试用会员";
        }
    }
    
    public double getFineAmount() { return fineAmount; }
    public void addFine(double amount) { this.fineAmount += amount; }
    public void payFine(double amount) { this.fineAmount = Math.max(0, this.fineAmount - amount); }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { this.isActive = active; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public LocalDate getLastBorrowDate() { return lastBorrowDate; }
    public int getTotalBorrowedBooks() { return totalBorrowedBooks; }
    
    public List<String> getReservedBooks() { return new ArrayList<>(reservedBooks); }
    public void addReservation(String isbn) { 
        if (!reservedBooks.contains(isbn)) {
            reservedBooks.add(isbn);
            reservationDates.put(isbn, LocalDate.now());
        }
    }
    public void removeReservation(String isbn) { 
        reservedBooks.remove(isbn);
        reservationDates.remove(isbn);
    }
    
    public Map<String, LocalDate> getReservationDates() { return new HashMap<>(reservationDates); }
    
    @Override
    public String toString() {
        return String.format("读者: %s (ID: %s), 电话: %s, 邮箱: %s, 会员等级: %s, 积分: %d, 当前借阅: %d/%d, 欠款: ¥%.2f, 状态: %s",
                name, id, phone, email, membershipLevel, points, getCurrentBorrowedCount(), 
                getMaxBorrowedBooks(), fineAmount, isActive ? "正常" : "停用");
    }
    
    public String getDetailedInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 读者详细信息 ===\n");
        sb.append("姓名: ").append(name).append("\n");
        sb.append("ID: ").append(id).append("\n");
        sb.append("电话: ").append(phone).append("\n");
        sb.append("邮箱: ").append(email).append("\n");
        sb.append("地址: ").append(address).append("\n");
        sb.append("注册日期: ").append(registrationDate).append("\n");
        sb.append("会员等级: ").append(membershipLevel).append("\n");
        sb.append("当前积分: ").append(points).append("\n");
        sb.append("当前借阅: ").append(getCurrentBorrowedCount()).append("/").append(getMaxBorrowedBooks()).append("\n");
        sb.append("总借阅次数: ").append(totalBorrowedBooks).append("\n");
        sb.append("最后借阅日期: ").append(lastBorrowDate != null ? lastBorrowDate : "无").append("\n");
        sb.append("欠款金额: ¥").append(String.format("%.2f", fineAmount)).append("\n");
        sb.append("账户状态: ").append(isActive ? "正常" : "停用").append("\n");
        if (!reservedBooks.isEmpty()) {
            sb.append("预约图书: ").append(String.join(", ", reservedBooks)).append("\n");
        }
        return sb.toString();
    }
}

/**
 * 借阅记录类
 */
class BorrowingRecord {
    private String readerId;
    private String bookIsbn;
    private LocalDate borrowDate;
    private LocalDate dueDate;
    private LocalDate returnDate;
    private static final int BORROW_PERIOD_DAYS = 30;
    private boolean isRenewed;
    private int renewCount;
    private static final int MAX_RENEW_COUNT = 2;
    private static final double DAILY_FINE = 0.5; // 每天罚金0.5元
    private double fineAmount;
    private boolean finePaid;
    private String notes; // 备注
    
    public BorrowingRecord(String readerId, String bookIsbn) {
        this.readerId = readerId;
        this.bookIsbn = bookIsbn;
        this.borrowDate = LocalDate.now();
        this.dueDate = borrowDate.plusDays(BORROW_PERIOD_DAYS);
        this.isRenewed = false;
        this.renewCount = 0;
        this.fineAmount = 0.0;
        this.finePaid = true;
        this.notes = "";
    }
    
    public BorrowingRecord(String readerId, String bookIsbn, int borrowPeriodDays) {
        this.readerId = readerId;
        this.bookIsbn = bookIsbn;
        this.borrowDate = LocalDate.now();
        this.dueDate = borrowDate.plusDays(borrowPeriodDays);
        this.isRenewed = false;
        this.renewCount = 0;
        this.fineAmount = 0.0;
        this.finePaid = true;
        this.notes = "";
    }
    
    public String getReaderId() { return readerId; }
    public String getBookIsbn() { return bookIsbn; }
    public LocalDate getBorrowDate() { return borrowDate; }
    public LocalDate getDueDate() { return dueDate; }
    public LocalDate getReturnDate() { return returnDate; }
    public void setReturnDate(LocalDate returnDate) { 
        this.returnDate = returnDate;
        if (isOverdue()) {
            calculateFine();
        }
    }
    
    public boolean isRenewed() { return isRenewed; }
    public int getRenewCount() { return renewCount; }
    public boolean canRenew() { return renewCount < MAX_RENEW_COUNT && !isOverdue() && returnDate == null; }
    
    public boolean renewBook() {
        if (canRenew()) {
            this.dueDate = dueDate.plusDays(BORROW_PERIOD_DAYS);
            this.isRenewed = true;
            this.renewCount++;
            return true;
        }
        return false;
    }
    
    public double getFineAmount() { return fineAmount; }
    public boolean isFinePaid() { return finePaid; }
    public void payFine() { this.finePaid = true; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public boolean isOverdue() {
        return returnDate == null && LocalDate.now().isAfter(dueDate);
    }
    
    public long getOverdueDays() {
        if (returnDate != null) return 0;
        LocalDate now = LocalDate.now();
        return now.isAfter(dueDate) ? now.toEpochDay() - dueDate.toEpochDay() : 0;
    }
    
    public void calculateFine() {
        long overdueDays = getOverdueDays();
        if (overdueDays > 0) {
            this.fineAmount = overdueDays * DAILY_FINE;
            this.finePaid = false;
        }
    }
    
    public double getCurrentFine() {
        if (returnDate != null) return fineAmount;
        calculateFine();
        return fineAmount;
    }
    
    @Override
    public String toString() {
        String status = returnDate != null ? "已归还" : (isOverdue() ? "逾期" : "借阅中");
        String renewInfo = isRenewed ? String.format(" (已续借%d次)", renewCount) : "";
        String fineInfo = fineAmount > 0 ? String.format(", 罚金: ¥%.2f", fineAmount) : "";
        
        return String.format("读者ID: %s, 图书ISBN: %s, 借阅日期: %s, 应还日期: %s, 状态: %s%s%s",
                readerId, bookIsbn, borrowDate, dueDate, status, renewInfo, fineInfo);
    }
    
    public String getDetailedInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 借阅记录详情 ===\n");
        sb.append("读者ID: ").append(readerId).append("\n");
        sb.append("图书ISBN: ").append(bookIsbn).append("\n");
        sb.append("借阅日期: ").append(borrowDate).append("\n");
        sb.append("应还日期: ").append(dueDate).append("\n");
        sb.append("归还日期: ").append(returnDate != null ? returnDate : "未归还").append("\n");
        sb.append("续借次数: ").append(renewCount).append("/").append(MAX_RENEW_COUNT).append("\n");
        sb.append("逾期天数: ").append(getOverdueDays()).append("\n");
        sb.append("罚金金额: ¥").append(String.format("%.2f", getCurrentFine())).append("\n");
        sb.append("罚金状态: ").append(finePaid ? "已缴纳" : "未缴纳").append("\n");
        if (!notes.isEmpty()) {
            sb.append("备注: ").append(notes).append("\n");
        }
        return sb.toString();
    }
}

/**
 * 图书馆类
 */
class Library {
    private String name;
    private Map<String, Book> books;
    private Map<String, Reader> readers;
    private List<BorrowingRecord> borrowingRecords;
    private List<String> operationLogs;
    private Map<String, List<String>> reservations; // ISBN -> List of ReaderIDs
    private static int borrowPeriodDays = 30;
    private static int maxBorrowedBooks = 5;
    private static double dailyFine = 0.5;
    
    public Library(String name) {
        this.name = name;
        this.books = new HashMap<>();
        this.readers = new HashMap<>();
        this.borrowingRecords = new ArrayList<>();
        this.operationLogs = new ArrayList<>();
        this.reservations = new HashMap<>();
        logOperation("图书馆系统启动: " + name);
    }
    
    private void logOperation(String operation) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        operationLogs.add(timestamp + " - " + operation);
    }
    
    public void addBook(Book book) {
        books.put(book.getIsbn(), book);
        book.getAuthor().addBook(book);
        logOperation("添加图书: " + book.getTitle() + " (ISBN: " + book.getIsbn() + ")");
    }
    
    public boolean deleteBook(String isbn) {
        Book book = books.get(isbn);
        if (book == null) return false;
        
        // 检查是否有未归还的借阅记录
        boolean hasActiveBorrowing = borrowingRecords.stream()
                .anyMatch(record -> record.getBookIsbn().equals(isbn) && record.getReturnDate() == null);
        
        if (hasActiveBorrowing) {
            return false;
        }
        
        books.remove(isbn);
        logOperation("删除图书: " + book.getTitle() + " (ISBN: " + isbn + ")");
        return true;
    }
    
    public void updateBook(String isbn, Scanner scanner) {
        Book book = books.get(isbn);
        if (book == null) {
            System.out.println("未找到该图书！");
            return;
        }
        
        System.out.println("当前图书信息:");
        System.out.println(book.getDetailedInfo());
        
        System.out.println("\n请输入新的信息（直接回车保持不变）:");
        
        System.out.print("书名 [" + book.getTitle() + "]: ");
        String title = scanner.nextLine();
        if (!title.trim().isEmpty()) {
            book.setTitle(title);
        }
        
        System.out.print("出版社 [" + book.getPublisher() + "]: ");
        String publisher = scanner.nextLine();
        if (!publisher.trim().isEmpty()) {
            book.setPublisher(publisher);
        }
        
        System.out.print("价格 [" + book.getPrice() + "]: ");
        String priceStr = scanner.nextLine();
        if (!priceStr.trim().isEmpty()) {
            try {
                double price = Double.parseDouble(priceStr);
                book.setPrice(price);
            } catch (NumberFormatException e) {
                System.out.println("价格格式错误，保持原值。");
            }
        }
        
        System.out.print("书架位置 [" + book.getLocation() + "]: ");
        String location = scanner.nextLine();
        if (!location.trim().isEmpty()) {
            book.setLocation(location);
        }
        
        System.out.print("描述 [" + book.getDescription() + "]: ");
        String description = scanner.nextLine();
        if (!description.trim().isEmpty()) {
            book.setDescription(description);
        }
        
        logOperation("更新图书信息: " + book.getTitle() + " (ISBN: " + isbn + ")");
        System.out.println("图书信息更新成功！");
    }
    
    public void addReader(Reader reader) {
        readers.put(reader.getId(), reader);
        logOperation("添加读者: " + reader.getName() + " (ID: " + reader.getId() + ")");
    }
    
    public boolean deleteReader(String readerId) {
        Reader reader = readers.get(readerId);
        if (reader == null) return false;
        
        // 检查是否有未归还的图书
        if (reader.getCurrentBorrowedCount() > 0) {
            return false;
        }
        
        // 检查是否有未缴纳的罚金
        if (reader.getFineAmount() > 0) {
            return false;
        }
        
        readers.remove(readerId);
        logOperation("删除读者: " + reader.getName() + " (ID: " + readerId + ")");
        return true;
    }
    
    public void updateReader(String readerId, Scanner scanner) {
        Reader reader = readers.get(readerId);
        if (reader == null) {
            System.out.println("未找到该读者！");
            return;
        }
        
        System.out.println("当前读者信息:");
        System.out.println(reader.getDetailedInfo());
        
        System.out.println("\n请输入新的信息（直接回车保持不变）:");
        
        System.out.print("姓名 [" + reader.getName() + "]: ");
        String name = scanner.nextLine();
        if (!name.trim().isEmpty()) {
            reader.setName(name);
        }
        
        System.out.print("电话 [" + reader.getPhone() + "]: ");
        String phone = scanner.nextLine();
        if (!phone.trim().isEmpty()) {
            reader.setPhone(phone);
        }
        
        System.out.print("邮箱 [" + reader.getEmail() + "]: ");
        String email = scanner.nextLine();
        if (!email.trim().isEmpty()) {
            reader.setEmail(email);
        }
        
        System.out.print("地址 [" + reader.getAddress() + "]: ");
        String address = scanner.nextLine();
        if (!address.trim().isEmpty()) {
            reader.setAddress(address);
        }
        
        logOperation("更新读者信息: " + reader.getName() + " (ID: " + readerId + ")");
        System.out.println("读者信息更新成功！");
    }
    
    public List<Book> searchBooks(String keyword) {
        return books.values().stream()
                .filter(book -> book.getTitle().toLowerCase().contains(keyword.toLowerCase()) ||
                               book.getAuthor().getName().toLowerCase().contains(keyword.toLowerCase()) ||
                               book.getCategory().toLowerCase().contains(keyword.toLowerCase()) ||
                               book.getIsbn().contains(keyword) ||
                               book.getPublisher().toLowerCase().contains(keyword.toLowerCase()))
                .collect(Collectors.toList());
    }
    
    public void searchReader(String keyword) {
        List<Reader> results = readers.values().stream()
                .filter(reader -> reader.getName().toLowerCase().contains(keyword.toLowerCase()) ||
                                 reader.getId().contains(keyword) ||
                                 reader.getPhone().contains(keyword) ||
                                 reader.getEmail().toLowerCase().contains(keyword.toLowerCase()))
                .collect(Collectors.toList());
        
        if (results.isEmpty()) {
            System.out.println("未找到相关读者。");
        } else {
            System.out.println("搜索结果:");
            results.forEach(System.out::println);
        }
    }
    
    public void displayBooksByCategory(String category) {
        List<Book> categoryBooks = books.values().stream()
                .filter(book -> book.getCategory().equalsIgnoreCase(category))
                .collect(Collectors.toList());
        
        if (categoryBooks.isEmpty()) {
            System.out.println("该分类下没有图书。");
        } else {
            System.out.println("\n=== " + category + " 分类图书 ===");
            categoryBooks.forEach(System.out::println);
        }
    }
    
    public void addBookCopies(String isbn, int count) {
        Book book = books.get(isbn);
        if (book != null) {
            book.setTotalCopies(book.getTotalCopies() + count);
            book.setAvailableCopies(book.getAvailableCopies() + count);
            logOperation("增加图书副本: " + book.getTitle() + " +" + count + "本");
            System.out.println("成功增加 " + count + " 本《" + book.getTitle() + "》");
        } else {
            System.out.println("未找到该图书！");
        }
    }
    
    public void removeBookCopies(String isbn, int count) {
        Book book = books.get(isbn);
        if (book != null) {
            int currentBorrowed = book.getTotalCopies() - book.getAvailableCopies();
            int newTotal = Math.max(currentBorrowed, book.getTotalCopies() - count);
            int newAvailable = Math.max(0, newTotal - currentBorrowed);
            
            book.setTotalCopies(newTotal);
            book.setAvailableCopies(newAvailable);
            logOperation("减少图书副本: " + book.getTitle() + " -" + count + "本");
            System.out.println("成功减少 " + count + " 本《" + book.getTitle() + "》");
        } else {
            System.out.println("未找到该图书！");
        }
    }
    
    public void displayInventoryStatus() {
        System.out.println("\n=== 图书库存状态 ===");
        books.values().stream()
                .sorted((b1, b2) -> b1.getTitle().compareTo(b2.getTitle()))
                .forEach(book -> {
                    System.out.printf("《%s》- 总数: %d, 可借: %d, 已借: %d%n",
                            book.getTitle(), book.getTotalCopies(),
                            book.getAvailableCopies(),
                            book.getTotalCopies() - book.getAvailableCopies());
                });
    }
    
    public void displayReaderBorrowingHistory(String readerId) {
        Reader reader = readers.get(readerId);
        if (reader == null) {
            System.out.println("未找到该读者！");
            return;
        }
        
        System.out.println("\n=== " + reader.getName() + " 的借阅历史 ===");
        List<BorrowingRecord> history = reader.getBorrowingHistory();
        if (history.isEmpty()) {
            System.out.println("暂无借阅记录。");
        } else {
            history.forEach(System.out::println);
        }
    }
    
    public void displayReaderPoints(String readerId) {
        Reader reader = readers.get(readerId);
        if (reader == null) {
            System.out.println("未找到该读者！");
            return;
        }
        
        System.out.println("\n=== " + reader.getName() + " 的积分信息 ===");
        System.out.println("当前积分: " + reader.getPoints());
        System.out.println("会员等级: " + reader.getMembershipLevel());
        System.out.println("可借阅数量: " + reader.getMaxBorrowedBooks());
        
        // 积分使用记录
        System.out.println("\n积分规则:");
        System.out.println("- 每次借阅获得10积分");
        System.out.println("- 按时归还获得5积分");
        System.out.println("- 逾期归还扣除20积分");
        System.out.println("- 100-499积分: 普通会员(最多借5本)");
        System.out.println("- 500-999积分: 高级会员(最多借8本)");
        System.out.println("- 1000+积分: VIP会员(最多借10本)");
    }
    
    public boolean borrowBook(String readerId, String isbn) {
        Reader reader = readers.get(readerId);
        Book book = books.get(isbn);
        
        if (reader == null || book == null || !book.isAvailable() || !reader.canBorrowMore()) {
            return false;
        }
        
        // 如果读者预约了这本书，优先处理
        if (reader.getReservedBooks().contains(isbn)) {
            reader.removeReservation(isbn);
            removeReservation(isbn, readerId);
        }
        
        if (book.borrowCopy()) {
            BorrowingRecord record = new BorrowingRecord(readerId, isbn, borrowPeriodDays);
            borrowingRecords.add(record);
            reader.addBorrowingRecord(record);
            logOperation("借阅图书: " + reader.getName() + " 借阅《" + book.getTitle() + "》");
            return true;
        }
        return false;
    }
    
    public boolean returnBook(String readerId, String isbn) {
        for (BorrowingRecord record : borrowingRecords) {
            if (record.getReaderId().equals(readerId) && 
                record.getBookIsbn().equals(isbn) && 
                record.getReturnDate() == null) {
                
                record.setReturnDate(LocalDate.now());
                Book book = books.get(isbn);
                Reader reader = readers.get(readerId);
                
                if (book != null) {
                    book.returnCopy();
                }
                
                // 处理罚金
                if (record.isOverdue()) {
                    double fine = record.getCurrentFine();
                    reader.addFine(fine);
                    reader.deductPoints(20); // 逾期扣除积分
                } else {
                    reader.addPoints(5); // 按时归还奖励积分
                }
                
                logOperation("归还图书: " + reader.getName() + " 归还《" + book.getTitle() + "》");
                
                // 检查是否有人预约这本书
                notifyNextReserver(isbn);
                
                return true;
            }
        }
        return false;
    }
    
    public boolean renewBook(String readerId, String isbn) {
        for (BorrowingRecord record : borrowingRecords) {
            if (record.getReaderId().equals(readerId) && 
                record.getBookIsbn().equals(isbn) && 
                record.getReturnDate() == null) {
                
                if (record.renewBook()) {
                    Reader reader = readers.get(readerId);
                    Book book = books.get(isbn);
                    logOperation("续借图书: " + reader.getName() + " 续借《" + book.getTitle() + "》");
                    return true;
                }
                return false;
            }
        }
        return false;
    }
    
    public boolean reserveBook(String readerId, String isbn) {
        Reader reader = readers.get(readerId);
        Book book = books.get(isbn);
        
        if (reader == null || book == null) {
            return false;
        }
        
        if (book.isAvailable()) {
            System.out.println("图书可直接借阅，无需预约。");
            return false;
        }
        
        reader.addReservation(isbn);
        reservations.computeIfAbsent(isbn, k -> new ArrayList<>()).add(readerId);
        logOperation("预约图书: " + reader.getName() + " 预约《" + book.getTitle() + "》");
        return true;
    }
    
    public boolean cancelReservation(String readerId, String isbn) {
        Reader reader = readers.get(readerId);
        if (reader == null || !reader.getReservedBooks().contains(isbn)) {
            return false;
        }
        
        reader.removeReservation(isbn);
        removeReservation(isbn, readerId);
        
        Book book = books.get(isbn);
        logOperation("取消预约: " + reader.getName() + " 取消预约《" + book.getTitle() + "》");
        return true;
    }
    
    private void removeReservation(String isbn, String readerId) {
        List<String> reserverList = reservations.get(isbn);
        if (reserverList != null) {
            reserverList.remove(readerId);
            if (reserverList.isEmpty()) {
                reservations.remove(isbn);
            }
        }
    }
    
    private void notifyNextReserver(String isbn) {
        List<String> reserverList = reservations.get(isbn);
        if (reserverList != null && !reserverList.isEmpty()) {
            String nextReaderId = reserverList.get(0);
            Reader nextReader = readers.get(nextReaderId);
            Book book = books.get(isbn);
            
            if (nextReader != null && book != null) {
                System.out.println("通知: " + nextReader.getName() + "，您预约的《" + 
                                 book.getTitle() + "》已可借阅，请尽快前来借阅。");
            }
        }
    }
    
    public void calculateAndDisplayFines(String readerId) {
        Reader reader = readers.get(readerId);
        if (reader == null) {
            System.out.println("未找到该读者！");
            return;
        }
        
        System.out.println("\n=== " + reader.getName() + " 的罚金信息 ===");
        System.out.println("当前欠款: ¥" + String.format("%.2f", reader.getFineAmount()));
        
        // 计算当前逾期图书的罚金
        double currentOverdueFines = 0.0;
        List<BorrowingRecord> overdueRecords = borrowingRecords.stream()
                .filter(record -> record.getReaderId().equals(readerId) && 
                                record.getReturnDate() == null && 
                                record.isOverdue())
                .collect(Collectors.toList());
        
        if (!overdueRecords.isEmpty()) {
            System.out.println("\n当前逾期图书:");
            for (BorrowingRecord record : overdueRecords) {
                double fine = record.getCurrentFine();
                currentOverdueFines += fine;
                Book book = books.get(record.getBookIsbn());
                System.out.printf("《%s》- 逾期%d天, 罚金: ¥%.2f%n", 
                                book.getTitle(), record.getOverdueDays(), fine);
            }
            System.out.printf("当前逾期罚金总计: ¥%.2f%n", currentOverdueFines);
        }
        
        System.out.printf("总欠款: ¥%.2f%n", reader.getFineAmount() + currentOverdueFines);
    }
    
    public void displayAllBooks() {
        System.out.println("\n=== 所有图书 ===");
        books.values().forEach(System.out::println);
    }
    
    public void displayAllReaders() {
        System.out.println("\n=== 所有读者 ===");
        readers.values().forEach(System.out::println);
    }
    
    public void displayBorrowingRecords() {
        System.out.println("\n=== 借阅记录 ===");
        if (borrowingRecords.isEmpty()) {
            System.out.println("暂无借阅记录。");
        } else {
            borrowingRecords.stream()
                    .sorted((r1, r2) -> r2.getBorrowDate().compareTo(r1.getBorrowDate()))
                    .forEach(System.out::println);
        }
    }
    
    public void displayOverdueRecords() {
        System.out.println("\n=== 逾期借阅记录 ===");
        List<BorrowingRecord> overdueRecords = borrowingRecords.stream()
                .filter(record -> record.isOverdue())
                .sorted((r1, r2) -> Long.compare(r2.getOverdueDays(), r1.getOverdueDays()))
                .collect(Collectors.toList());
        
        if (overdueRecords.isEmpty()) {
            System.out.println("暂无逾期记录。");
        } else {
            overdueRecords.forEach(record -> {
                Book book = books.get(record.getBookIsbn());
                Reader reader = readers.get(record.getReaderId());
                System.out.printf("《%s》- 借阅者: %s, 逾期%d天, 罚金: ¥%.2f%n",
                        book.getTitle(), reader.getName(), 
                        record.getOverdueDays(), record.getCurrentFine());
            });
        }
    }
    
    public void generateBorrowingStatistics() {
        System.out.println("\n=== 借阅统计分析 ===");
        
        // 按月统计借阅量
        Map<String, Long> monthlyStats = borrowingRecords.stream()
                .collect(Collectors.groupingBy(
                        record -> record.getBorrowDate().format(DateTimeFormatter.ofPattern("yyyy-MM")),
                        Collectors.counting()));
        
        System.out.println("月度借阅统计:");
        monthlyStats.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> System.out.println("  " + entry.getKey() + ": " + entry.getValue() + " 次"));
        
        // 分类借阅统计
        Map<String, Long> categoryStats = borrowingRecords.stream()
                .collect(Collectors.groupingBy(
                        record -> books.get(record.getBookIsbn()).getCategory(),
                        Collectors.counting()));
        
        System.out.println("\n分类借阅统计:");
        categoryStats.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .forEach(entry -> System.out.println("  " + entry.getKey() + ": " + entry.getValue() + " 次"));
        
        // 读者活跃度统计
        Map<String, Long> readerStats = borrowingRecords.stream()
                .collect(Collectors.groupingBy(
                        BorrowingRecord::getReaderId,
                        Collectors.counting()));
        
        System.out.println("\n读者借阅排行 (前10名):");
        readerStats.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(10)
                .forEach(entry -> {
                    Reader reader = readers.get(entry.getKey());
                    System.out.println("  " + reader.getName() + ": " + entry.getValue() + " 次");
                });
    }
    
    public void displayPopularBooks() {
        System.out.println("\n=== 热门图书排行榜 ===");
        books.values().stream()
                .sorted((b1, b2) -> Integer.compare(b2.getBorrowCount(), b1.getBorrowCount()))
                .limit(10)
                .forEach(book -> {
                    System.out.printf("《%s》- 借阅次数: %d, 评分: %.1f%n",
                            book.getTitle(), book.getBorrowCount(), book.getRating());
                });
    }
    
    public void displayActiveReaders() {
        System.out.println("\n=== 活跃读者排行榜 ===");
        readers.values().stream()
                .sorted((r1, r2) -> Integer.compare(r2.getTotalBorrowedBooks(), r1.getTotalBorrowedBooks()))
                .limit(10)
                .forEach(reader -> {
                    System.out.printf("%s - 总借阅: %d次, 积分: %d, 等级: %s%n",
                            reader.getName(), reader.getTotalBorrowedBooks(),
                            reader.getPoints(), reader.getMembershipLevel());
                });
    }
    
    public void generateIncomeReport() {
        System.out.println("\n=== 收入统计报告 ===");
        
        double totalFines = borrowingRecords.stream()
                .mapToDouble(BorrowingRecord::getFineAmount)
                .sum();
        
        double pendingFines = readers.values().stream()
                .mapToDouble(Reader::getFineAmount)
                .sum();
        
        double currentOverdueFines = borrowingRecords.stream()
                .filter(record -> record.isOverdue() && record.getReturnDate() == null)
                .mapToDouble(BorrowingRecord::getCurrentFine)
                .sum();
        
        System.out.printf("已收罚金总额: ¥%.2f%n", totalFines);
        System.out.printf("待收罚金: ¥%.2f%n", pendingFines);
        System.out.printf("当前逾期罚金: ¥%.2f%n", currentOverdueFines);
        System.out.printf("预计总收入: ¥%.2f%n", totalFines + pendingFines + currentOverdueFines);
        
        // 按月统计罚金收入
        Map<String, Double> monthlyFines = borrowingRecords.stream()
                .filter(record -> record.getReturnDate() != null && record.getFineAmount() > 0)
                .collect(Collectors.groupingBy(
                        record -> record.getReturnDate().format(DateTimeFormatter.ofPattern("yyyy-MM")),
                        Collectors.summingDouble(BorrowingRecord::getFineAmount)));
        
        System.out.println("\n月度罚金收入:");
        monthlyFines.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> System.out.printf("  %s: ¥%.2f%n", entry.getKey(), entry.getValue()));
    }
    
    public void searchBooksByPriceRange(double minPrice, double maxPrice) {
        System.out.println("\n=== 价格区间搜索结果 ===");
        List<Book> results = books.values().stream()
                .filter(book -> book.getPrice() >= minPrice && book.getPrice() <= maxPrice)
                .sorted((b1, b2) -> Double.compare(b1.getPrice(), b2.getPrice()))
                .collect(Collectors.toList());
        
        if (results.isEmpty()) {
            System.out.println("该价格区间内没有图书。");
        } else {
            results.forEach(System.out::println);
        }
    }
    
    public void searchBooksByYearRange(int startYear, int endYear) {
        System.out.println("\n=== 年份区间搜索结果 ===");
        List<Book> results = books.values().stream()
                .filter(book -> book.getPublicationYear() >= startYear && 
                               book.getPublicationYear() <= endYear)
                .sorted((b1, b2) -> Integer.compare(b2.getPublicationYear(), b1.getPublicationYear()))
                .collect(Collectors.toList());
        
        if (results.isEmpty()) {
            System.out.println("该年份区间内没有图书。");
        } else {
            results.forEach(System.out::println);
        }
    }
    
    public void multiCriteriaSearch(Scanner scanner) {
        System.out.println("\n=== 多条件组合搜索 ===");
        
        System.out.print("分类 (留空表示不限): ");
        String category = scanner.nextLine().trim();
        
        System.out.print("最低价格 (留空表示不限): ");
        String minPriceStr = scanner.nextLine().trim();
        
        System.out.print("最高价格 (留空表示不限): ");
        String maxPriceStr = scanner.nextLine().trim();
        
        System.out.print("起始年份 (留空表示不限): ");
        String startYearStr = scanner.nextLine().trim();
        
        System.out.print("结束年份 (留空表示不限): ");
        String endYearStr = scanner.nextLine().trim();
        
        System.out.print("关键词 (留空表示不限): ");
        String keyword = scanner.nextLine().trim();
        
        List<Book> results = books.values().stream()
                .filter(book -> {
                    if (!category.isEmpty() && !book.getCategory().toLowerCase().contains(category.toLowerCase())) {
                        return false;
                    }
                    if (!minPriceStr.isEmpty()) {
                        try {
                            double minPrice = Double.parseDouble(minPriceStr);
                            if (book.getPrice() < minPrice) return false;
                        } catch (NumberFormatException e) {}
                    }
                    if (!maxPriceStr.isEmpty()) {
                        try {
                            double maxPrice = Double.parseDouble(maxPriceStr);
                            if (book.getPrice() > maxPrice) return false;
                        } catch (NumberFormatException e) {}
                    }
                    if (!startYearStr.isEmpty()) {
                        try {
                            int startYear = Integer.parseInt(startYearStr);
                            if (book.getPublicationYear() < startYear) return false;
                        } catch (NumberFormatException e) {}
                    }
                    if (!endYearStr.isEmpty()) {
                        try {
                            int endYear = Integer.parseInt(endYearStr);
                            if (book.getPublicationYear() > endYear) return false;
                        } catch (NumberFormatException e) {}
                    }
                    if (!keyword.isEmpty()) {
                        String lowerKeyword = keyword.toLowerCase();
                        if (!book.getTitle().toLowerCase().contains(lowerKeyword) &&
                            !book.getAuthor().getName().toLowerCase().contains(lowerKeyword) &&
                            !book.getDescription().toLowerCase().contains(lowerKeyword)) {
                            return false;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());
        
        System.out.println("\n搜索结果 (" + results.size() + " 本):");
        if (results.isEmpty()) {
            System.out.println("未找到符合条件的图书。");
        } else {
            results.forEach(System.out::println);
        }
    }
    
    public void generateReport() {
        System.out.println("\n=== 图书馆统计报告 ===");
        System.out.println("图书馆名称: " + name);
        System.out.println("报告生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 基础统计
        int totalBooks = books.values().stream().mapToInt(Book::getTotalCopies).sum();
        int availableBooks = books.values().stream().mapToInt(Book::getAvailableCopies).sum();
        int borrowedBooks = totalBooks - availableBooks;
        
        System.out.println("\n=== 基础数据 ===");
        System.out.println("图书种类数: " + books.size());
        System.out.println("总图书册数: " + totalBooks);
        System.out.println("可借阅册数: " + availableBooks);
        System.out.println("已借出册数: " + borrowedBooks);
        System.out.println("注册读者数: " + readers.size());
        System.out.println("总借阅记录数: " + borrowingRecords.size());
        
        // 逾期统计
        long overdueCount = borrowingRecords.stream().filter(BorrowingRecord::isOverdue).count();
        double totalFines = borrowingRecords.stream().mapToDouble(BorrowingRecord::getCurrentFine).sum();
        
        System.out.println("\n=== 逾期情况 ===");
        System.out.println("逾期未还图书数: " + overdueCount);
        System.out.println("待收罚金总额: ¥" + String.format("%.2f", totalFines));
        
        // 分类统计
        System.out.println("\n=== 图书分类分布 ===");
        Map<String, Long> categoryCount = books.values().stream()
                .collect(Collectors.groupingBy(Book::getCategory, Collectors.counting()));
        
        categoryCount.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> System.out.println("  " + entry.getKey() + ": " + entry.getValue() + " 种"));
        
        // 会员等级分布
        System.out.println("\n=== 会员等级分布 ===");
        Map<String, Long> membershipStats = readers.values().stream()
                .collect(Collectors.groupingBy(Reader::getMembershipLevel, Collectors.counting()));
        
        membershipStats.forEach((level, count) -> 
                System.out.println("  " + level + ": " + count + " 人"));
        
        // 图书馆利用率
        double utilizationRate = totalBooks > 0 ? (double) borrowedBooks / totalBooks * 100 : 0;
        System.out.println("\n=== 利用率分析 ===");
        System.out.printf("图书利用率: %.2f%%%n", utilizationRate);
        
        long activeReaders = readers.values().stream()
                .filter(reader -> reader.getCurrentBorrowedCount() > 0)
                .count();
        double readerActivity = readers.size() > 0 ? (double) activeReaders / readers.size() * 100 : 0;
        System.out.printf("读者活跃率: %.2f%%%n", readerActivity);
    }
    
    // 数据导出功能
    public void exportBooksToCSV() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("books_export.csv"))) {
            writer.println("ISBN,书名,作者,分类,出版年份,出版社,价格,总册数,可借册数,借阅次数,评分,位置");
            books.values().forEach(book -> {
                writer.printf("%s,%s,%s,%s,%d,%s,%.2f,%d,%d,%d,%.1f,%s%n",
                        book.getIsbn(), book.getTitle(), book.getAuthor().getName(),
                        book.getCategory(), book.getPublicationYear(), book.getPublisher(),
                        book.getPrice(), book.getTotalCopies(), book.getAvailableCopies(),
                        book.getBorrowCount(), book.getRating(), book.getLocation());
            });
            System.out.println("图书数据已导出到 books_export.csv");
            logOperation("导出图书数据到CSV文件");
        } catch (IOException e) {
            System.out.println("导出失败: " + e.getMessage());
        }
    }
    
    public void exportReadersToCSV() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("readers_export.csv"))) {
            writer.println("读者ID,姓名,电话,邮箱,地址,注册日期,会员等级,积分,总借阅次数,当前借阅数,欠款金额,账户状态");
            readers.values().forEach(reader -> {
                writer.printf("%s,%s,%s,%s,%s,%s,%s,%d,%d,%d,%.2f,%s%n",
                        reader.getId(), reader.getName(), reader.getPhone(), reader.getEmail(),
                        reader.getAddress(), reader.getRegistrationDate(), reader.getMembershipLevel(),
                        reader.getPoints(), reader.getTotalBorrowedBooks(), reader.getCurrentBorrowedCount(),
                        reader.getFineAmount(), reader.isActive() ? "正常" : "停用");
            });
            System.out.println("读者数据已导出到 readers_export.csv");
            logOperation("导出读者数据到CSV文件");
        } catch (IOException e) {
            System.out.println("导出失败: " + e.getMessage());
        }
    }
    
    public void exportBorrowingRecordsToCSV() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("borrowing_records_export.csv"))) {
            writer.println("读者ID,图书ISBN,借阅日期,应还日期,归还日期,续借次数,逾期天数,罚金金额,状态");
            borrowingRecords.forEach(record -> {
                String status = record.getReturnDate() != null ? "已归还" : 
                               (record.isOverdue() ? "逾期" : "借阅中");
                writer.printf("%s,%s,%s,%s,%s,%d,%d,%.2f,%s%n",
                        record.getReaderId(), record.getBookIsbn(), record.getBorrowDate(),
                        record.getDueDate(), record.getReturnDate() != null ? record.getReturnDate() : "",
                        record.getRenewCount(), record.getOverdueDays(), record.getCurrentFine(), status);
            });
            System.out.println("借阅记录已导出到 borrowing_records_export.csv");
            logOperation("导出借阅记录到CSV文件");
        } catch (IOException e) {
            System.out.println("导出失败: " + e.getMessage());
        }
    }
    
    public void exportStatisticsReport() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("statistics_report.txt"))) {
            writer.println("=== 图书馆统计报告 ===");
            writer.println("生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            writer.println();
            
            // 重定向System.out到文件
            PrintStream originalOut = System.out;
            System.setOut(new PrintStream(new FileOutputStream("temp_report.txt")));
            generateReport();
            generateBorrowingStatistics();
            System.setOut(originalOut);
            
            // 读取临时文件内容并写入最终报告
            Scanner tempScanner = new Scanner(new File("temp_report.txt"));
            while (tempScanner.hasNextLine()) {
                writer.println(tempScanner.nextLine());
            }
            tempScanner.close();
            new File("temp_report.txt").delete();
            
            System.out.println("统计报告已导出到 statistics_report.txt");
            logOperation("导出统计报告");
        } catch (IOException e) {
            System.out.println("导出失败: " + e.getMessage());
        }
    }
    
    // 数据备份和恢复
    public void backupData() {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String backupDir = "backup_" + timestamp;
            new File(backupDir).mkdirs();
            
            // 备份图书数据
            try (ObjectOutputStream oos = new ObjectOutputStream(
                    new FileOutputStream(backupDir + "/books.dat"))) {
                oos.writeObject(new HashMap<>(books));
            }
            
            // 备份读者数据
            try (ObjectOutputStream oos = new ObjectOutputStream(
                    new FileOutputStream(backupDir + "/readers.dat"))) {
                oos.writeObject(new HashMap<>(readers));
            }
            
            // 备份借阅记录
            try (ObjectOutputStream oos = new ObjectOutputStream(
                    new FileOutputStream(backupDir + "/records.dat"))) {
                oos.writeObject(new ArrayList<>(borrowingRecords));
            }
            
            // 备份操作日志
            try (PrintWriter writer = new PrintWriter(new FileWriter(backupDir + "/logs.txt"))) {
                operationLogs.forEach(writer::println);
            }
            
            System.out.println("数据备份完成，备份目录: " + backupDir);
            logOperation("数据备份到目录: " + backupDir);
        } catch (IOException e) {
            System.out.println("备份失败: " + e.getMessage());
        }
    }
    
    public void restoreData() {
        System.out.println("数据恢复功能需要指定备份目录，当前仅为演示。");
        logOperation("尝试恢复数据（演示模式）");
    }
    
    public void cleanExpiredData() {
        // 清理30天前的操作日志
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        int originalSize = operationLogs.size();
        operationLogs.removeIf(log -> {
            try {
                String dateStr = log.substring(0, 19);
                LocalDateTime logDate = LocalDateTime.parse(dateStr, formatter);
                return logDate.isBefore(cutoffDate);
            } catch (Exception e) {
                return false;
            }
        });
        
        int removedCount = originalSize - operationLogs.size();
        System.out.println("已清理 " + removedCount + " 条过期日志记录");
        logOperation("清理过期数据，删除 " + removedCount + " 条日志");
    }
    
    public void systemSettings(int choice, Scanner scanner) {
        switch (choice) {
            case 1:
                System.out.print("请输入新的借阅期限（天）[当前: " + borrowPeriodDays + "]: ");
                try {
                    int newPeriod = scanner.nextInt();
                    borrowPeriodDays = newPeriod;
                    System.out.println("借阅期限已更新为 " + borrowPeriodDays + " 天");
                    logOperation("修改借阅期限为 " + borrowPeriodDays + " 天");
                } catch (Exception e) {
                    System.out.println("输入格式错误");
                }
                break;
            case 2:
                System.out.print("请输入新的最大借阅数量[当前: " + maxBorrowedBooks + "]: ");
                try {
                    int newMax = scanner.nextInt();
                    maxBorrowedBooks = newMax;
                    System.out.println("最大借阅数量已更新为 " + maxBorrowedBooks + " 本");
                    logOperation("修改最大借阅数量为 " + maxBorrowedBooks + " 本");
                } catch (Exception e) {
                    System.out.println("输入格式错误");
                }
                break;
            case 3:
                System.out.print("请输入新的每日罚金标准（元）[当前: " + dailyFine + "]: ");
                try {
                    double newFine = scanner.nextDouble();
                    dailyFine = newFine;
                    System.out.println("每日罚金标准已更新为 ¥" + dailyFine);
                    logOperation("修改每日罚金标准为 ¥" + dailyFine);
                } catch (Exception e) {
                    System.out.println("输入格式错误");
                }
                break;
            case 4:
                System.out.println("\n=== 当前系统设置 ===");
                System.out.println("借阅期限: " + borrowPeriodDays + " 天");
                System.out.println("最大借阅数量: " + maxBorrowedBooks + " 本");
                System.out.println("每日罚金: ¥" + dailyFine);
                break;
        }
    }
    
    public void saveDataToFile() {
        try {
            exportBooksToCSV();
            exportReadersToCSV();
            exportBorrowingRecordsToCSV();
            System.out.println("系统数据已自动保存");
        } catch (Exception e) {
            System.out.println("数据保存失败: " + e.getMessage());
        }
    }
    
    public void viewLogs() {
        System.out.println("\n=== 系统操作日志 ===");
        if (operationLogs.isEmpty()) {
            System.out.println("暂无操作日志");
        } else {
            operationLogs.stream()
                    .skip(Math.max(0, operationLogs.size() - 50)) // 显示最近50条
                    .forEach(System.out::println);
        }
    }
    
    public void initializeSampleData() {
        // 添加示例作者
        Author author1 = new Author("金庸", "著名武侠小说家，本名查良镛");
        Author author2 = new Author("路遥", "现实主义作家，代表作《平凡的世界》");
        Author author3 = new Author("余华", "当代文学作家，先锋派代表人物");
        Author author4 = new Author("莫言", "诺贝尔文学奖获得者");
        Author author5 = new Author("刘慈欣", "科幻小说作家");
        Author author6 = new Author("村上春树", "日本当代作家");
        Author author7 = new Author("东野圭吾", "日本推理小说家");
        Author author8 = new Author("李娟", "当代散文作家，阿勒泰系列作者");
        Author author9 = new Author("王小波", "当代作家，自由主义的代表人物");
        Author author10 = new Author("钱钟书", "文学家、文学研究家");
        Author author11 = new Author("沈从文", "现代文学家");
        Author author12 = new Author("林语堂", "文学家、翻译家");
        Author author13 = new Author("鲁迅", "文学家、思想家");
        Author author14 = new Author("老舍", "现代著名作家");
        Author author15 = new Author("巴金", "现代文学家");
        Author author16 = new Author("冰心", "现代作家、诗人");
        Author author17 = new Author("史铁生", "当代作家");
        Author author18 = new Author("汪曾祺", "当代作家");
        Author author19 = new Author("梁实秋", "散文家、翻译家");
        Author author20 = new Author("三毛", "台湾作家");
        Author author21 = new Author("张爱玲", "现代女作家");
        Author author22 = new Author("阿加莎·克里斯蒂", "英国推理小说女王");
        Author author23 = new Author("乔治·奥威尔", "英国作家");
        Author author24 = new Author("加西亚·马尔克斯", "哥伦比亚作家，魔幻现实主义代表");
        Author author25 = new Author("海明威", "美国作家");
        Author author26 = new Author("卡夫卡", "奥地利作家");
        Author author27 = new Author("托尔斯泰", "俄国作家");
        Author author28 = new Author("陀思妥耶夫斯基", "俄国作家");
        Author author29 = new Author("雨果", "法国作家");
        Author author30 = new Author("司马迁", "史学家");
        
        // 添加示例图书（包含详细信息）
        Book book1 = new Book("射雕英雄传", author1, "978-7-108-01234-5", "武侠小说", 1957, 
                             "三联书店", 45.00, 3, "金庸武侠小说的开山之作，讲述郭靖成长的故事");
        book1.setLocation("A区-1层-001");
        book1.addKeyword("武侠");
        book1.addKeyword("江湖");
        book1.setRating(9.2);
        addBook(book1);
        
        Book book2 = new Book("神雕侠侣", author1, "978-7-108-01234-6", "武侠小说", 1959,
                             "三联书店", 48.00, 2, "射雕英雄传的续集，杨过与小龙女的爱情故事");
        book2.setLocation("A区-1层-002");
        book2.setRating(9.0);
        addBook(book2);
        
        Book book3 = new Book("平凡的世界", author2, "978-7-108-01234-7", "现代文学", 1986,
                             "人民文学出版社", 68.00, 5, "描写中国西北农村在改革开放前后的巨大变化");
        book3.setLocation("B区-2层-015");
        book3.setRating(9.5);
        addBook(book3);
        
        Book book4 = new Book("活着", author3, "978-7-108-01234-8", "现代文学", 1993,
                             "上海文艺出版社", 32.00, 4, "通过福贵的人生遭遇，展现中国几十年的社会变迁");
        book4.setLocation("B区-2层-025");
        book4.setRating(9.1);
        addBook(book4);
        
        Book book5 = new Book("许三观卖血记", author3, "978-7-108-01234-9", "现代文学", 1995,
                             "上海文艺出版社", 28.00, 2, "许三观靠卖血渡过人生难关的故事");
        book5.setLocation("B区-2层-026");
        book5.setRating(8.8);
        addBook(book5);
        
        Book book6 = new Book("红高粱", author4, "978-7-108-01235-0", "现代文学", 1987,
                             "作家出版社", 42.00, 3, "莫言成名作，魔幻现实主义风格");
        book6.setLocation("B区-2层-030");
        book6.setRating(8.9);
        addBook(book6);
        
        Book book7 = new Book("三体", author5, "978-7-108-01235-1", "科幻小说", 2006,
                             "重庆出版社", 55.00, 6, "中国科幻文学的里程碑之作");
        book7.setLocation("C区-3层-001");
        book7.setRating(9.3);
        addBook(book7);
        
        Book book8 = new Book("挪威的森林", author6, "978-7-108-01235-2", "外国文学", 1987,
                             "上海译文出版社", 35.00, 3, "村上春树的经典爱情小说");
        book8.setLocation("D区-3层-010");
        book8.setRating(8.5);
        addBook(book8);
        
        Book book9 = new Book("白夜行", author7, "978-7-108-01235-3", "推理小说", 1999,
                             "南海出版社", 39.00, 4, "东野圭吾最具代表性的推理小说");
        book9.setLocation("D区-3层-025");
        book9.setRating(9.0);
        addBook(book9);
        
        // 散文类
        Book book10 = new Book("冬牧场", author8, "978-7-108-01235-4", "散文", 2013,
                              "新星出版社", 42.00, 3, "李娟阿勒泰系列代表作，描写哈萨克牧民冬季转场生活");
        book10.setLocation("E区-2层-001");
        book10.addKeyword("散文");
        book10.addKeyword("新疆");
        book10.addKeyword("牧民生活");
        book10.setRating(9.1);
        addBook(book10);
        
        Book book11 = new Book("我的阿勒泰", author8, "978-7-108-01235-5", "散文", 2009,
                              "新星出版社", 38.00, 2, "李娟早期散文集，记录阿勒泰地区的生活");
        book11.setLocation("E区-2层-002");
        book11.setRating(8.9);
        addBook(book11);
        
        Book book12 = new Book("黄金时代", author9, "978-7-108-01235-6", "现代文学", 1994,
                              "花城出版社", 35.00, 3, "王小波时代三部曲之一");
        book12.setLocation("B区-2层-035");
        book12.setRating(9.2);
        addBook(book12);
        
        Book book13 = new Book("围城", author10, "978-7-108-01235-7", "现代文学", 1947,
                              "人民文学出版社", 45.00, 5, "钱钟书代表作，中国现代文学经典");
        book13.setLocation("B区-1层-001");
        book13.setRating(9.3);
        addBook(book13);
        
        Book book14 = new Book("边城", author11, "978-7-108-01235-8", "现代文学", 1934,
                              "人民文学出版社", 28.00, 4, "沈从文代表作，描写湘西风情");
        book14.setLocation("B区-1层-005");
        book14.setRating(9.0);
        addBook(book14);
        
        Book book15 = new Book("生活的艺术", author12, "978-7-108-01235-9", "人生哲学", 1937,
                              "湖南文艺出版社", 36.00, 3, "林语堂人生哲学代表作");
        book15.setLocation("F区-1层-010");
        book15.setRating(8.8);
        addBook(book15);
        
        // 经典文学
        Book book16 = new Book("朝花夕拾", author13, "978-7-108-01236-0", "散文", 1928,
                              "人民文学出版社", 25.00, 6, "鲁迅回忆性散文集");
        book16.setLocation("E区-1层-001");
        book16.setRating(9.1);
        addBook(book16);
        
        Book book17 = new Book("骆驼祥子", author14, "978-7-108-01236-1", "现代文学", 1939,
                              "人民文学出版社", 30.00, 5, "老舍代表作，描写旧北京人力车夫的悲惨生活");
        book17.setLocation("B区-1层-010");
        book17.setRating(9.0);
        addBook(book17);
        
        Book book18 = new Book("家", author15, "978-7-108-01236-2", "现代文学", 1931,
                              "人民文学出版社", 40.00, 4, "巴金激流三部曲之一");
        book18.setLocation("B区-1层-015");
        book18.setRating(8.9);
        addBook(book18);
        
        Book book19 = new Book("小桔灯", author16, "978-7-108-01236-3", "儿童文学", 1957,
                              "人民文学出版社", 22.00, 4, "冰心儿童文学代表作");
        book19.setLocation("G区-1层-001");
        book19.setRating(8.7);
        addBook(book19);
        
        Book book20 = new Book("我与地坛", author17, "978-7-108-01236-4", "散文", 1991,
                              "作家出版社", 32.00, 3, "史铁生散文代表作，思考生命与死亡");
        book20.setLocation("E区-2层-010");
        book20.setRating(9.4);
        addBook(book20);
        
        Book book21 = new Book("受戒", author18, "978-7-108-01236-5", "现代文学", 1980,
                              "人民文学出版社", 28.00, 3, "汪曾祺代表作，京派文学风格");
        book21.setLocation("B区-2层-040");
        book21.setRating(8.8);
        addBook(book21);
        
        Book book22 = new Book("雅舍小品", author19, "978-7-108-01236-6", "散文", 1949,
                              "天津人民出版社", 35.00, 2, "梁实秋散文代表作");
        book22.setLocation("E区-2层-015");
        book22.setRating(8.6);
        addBook(book22);
        
        Book book23 = new Book("撒哈拉的故事", author20, "978-7-108-01236-7", "散文", 1976,
                              "皇冠出版社", 38.00, 4, "三毛在撒哈拉沙漠的生活记录");
        book23.setLocation("E区-2层-020");
        book23.setRating(9.0);
        addBook(book23);
        
        Book book24 = new Book("半生缘", author21, "978-7-108-01236-8", "现代文学", 1951,
                              "北京十月文艺出版社", 42.00, 3, "张爱玲代表作，描写现代都市男女的爱情");
        book24.setLocation("B区-2层-045");
        book24.setRating(8.9);
        addBook(book24);
        
        // 外国文学经典
        Book book25 = new Book("东方快车谋杀案", author22, "978-7-108-01236-9", "推理小说", 1934,
                              "人民文学出版社", 35.00, 3, "阿加莎·克里斯蒂经典推理小说");
        book25.setLocation("D区-3层-030");
        book25.setRating(9.1);
        addBook(book25);
        
        Book book26 = new Book("1984", author23, "978-7-108-01237-0", "反乌托邦小说", 1949,
                              "上海译文出版社", 39.00, 4, "奥威尔反乌托邦文学经典");
        book26.setLocation("D区-4层-001");
        book26.setRating(9.3);
        addBook(book26);
        
        Book book27 = new Book("百年孤独", author24, "978-7-108-01237-1", "魔幻现实主义", 1967,
                              "南海出版社", 45.00, 5, "马尔克斯魔幻现实主义代表作");
        book27.setLocation("D区-4层-010");
        book27.setRating(9.4);
        addBook(book27);
        
        Book book28 = new Book("老人与海", author25, "978-7-108-01237-2", "外国文学", 1952,
                              "上海译文出版社", 28.00, 4, "海明威代表作，获普利策奖");
        book28.setLocation("D区-3层-040");
        book28.setRating(9.0);
        addBook(book28);
        
        Book book29 = new Book("变形记", author26, "978-7-108-01237-3", "现代主义文学", 1915,
                              "上海译文出版社", 25.00, 3, "卡夫卡代表作，现代主义文学经典");
        book29.setLocation("D区-4层-015");
        book29.setRating(8.8);
        addBook(book29);
        
        Book book30 = new Book("安娜·卡列尼娜", author27, "978-7-108-01237-4", "外国文学", 1877,
                              "人民文学出版社", 65.00, 3, "托尔斯泰代表作，俄国文学巨著");
        book30.setLocation("D区-4层-020");
        book30.setRating(9.2);
        addBook(book30);
        
        Book book31 = new Book("罪与罚", author28, "978-7-108-01237-5", "外国文学", 1866,
                              "上海译文出版社", 58.00, 2, "陀思妥耶夫斯基代表作");
        book31.setLocation("D区-4层-025");
        book31.setRating(9.1);
        addBook(book31);
        
        Book book32 = new Book("巴黎圣母院", author29, "978-7-108-01237-6", "外国文学", 1831,
                              "译林出版社", 48.00, 3, "雨果浪漫主义文学代表作");
        book32.setLocation("D区-4层-030");
        book32.setRating(8.9);
        addBook(book32);
        
        // 史学典籍
        Book book33 = new Book("史记", author30, "978-7-108-01237-7", "史学", -91,
                              "中华书局", 120.00, 2, "中国第一部纪传体通史，史学巨著");
        book33.setLocation("H区-1层-001");
        book33.setRating(9.5);
        addBook(book33);
        
        // 添加示例读者（包含详细信息）
        Reader reader1 = new Reader("张三", "R001", "13812345678", "<EMAIL>", "北京市朝阳区xxx街道");
        reader1.addPoints(150); // 额外积分，成为高级会员
        addReader(reader1);
        
        Reader reader2 = new Reader("李四", "R002", "13887654321", "<EMAIL>", "上海市浦东新区xxx路");
        reader2.addPoints(800); // VIP会员
        addReader(reader2);
        
        Reader reader3 = new Reader("王五", "R003", "13811111111", "<EMAIL>", "广州市天河区xxx大道");
        addReader(reader3);
        
        Reader reader4 = new Reader("赵六", "R004", "13922222222", "<EMAIL>", "深圳市南山区xxx街");
        reader4.addPoints(1200); // VIP会员
        addReader(reader4);
        
        Reader reader5 = new Reader("钱七", "R005", "13933333333", "<EMAIL>", "杭州市西湖区xxx路");
        addReader(reader5);
        
        // 模拟一些借阅记录
        borrowBook("R001", "978-7-108-01234-5"); // 张三借阅射雕英雄传
        borrowBook("R002", "978-7-108-01234-7"); // 李四借阅平凡的世界
        borrowBook("R002", "978-7-108-01235-1"); // 李四借阅三体
        borrowBook("R003", "978-7-108-01234-8"); // 王五借阅活着
        
        // 模拟一些预约
        reserveBook("R004", "978-7-108-01234-5"); // 赵六预约射雕英雄传
        reserveBook("R005", "978-7-108-01235-1"); // 钱七预约三体
        
        System.out.println("示例数据初始化完成！");
        System.out.println("- 已添加 33 本图书，涵盖多个类型：");
        System.out.println("  * 现代文学：12本（包括金庸、路遥、余华、莫言等）");
        System.out.println("  * 散文：6本（包括《冬牧场》、《我与地坛》等）");
        System.out.println("  * 外国文学：8本（包括《百年孤独》、《1984》等）");
        System.out.println("  * 推理小说：3本（东野圭吾、阿加莎等）");
        System.out.println("  * 科幻小说：1本（《三体》）");
        System.out.println("  * 儿童文学：1本（《小桔灯》）");
        System.out.println("  * 人生哲学：1本（《生活的艺术》）");
        System.out.println("  * 史学典籍：1本（《史记》）");
        System.out.println("- 已添加 5 位读者（不同会员等级）");
        System.out.println("- 已创建 4 条借阅记录");
        System.out.println("- 已创建 2 条预约记录");
        System.out.println("系统现在拥有丰富的藏书，可以进行各种功能测试。");
    }
}
